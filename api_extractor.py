#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API接口信息提取脚本
从Spring Boot项目中提取所有Controller类的接口信息并导出为Excel文件
"""

import os
import re
import pandas as pd
from pathlib import Path
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class APIExtractor:
    def __init__(self, project_root):
        self.project_root = Path(project_root)
        self.controller_path = self.project_root / "src/main/java/com/bonc/ioc/bzf/business"
        self.apis = []
        
    def extract_apis(self):
        """提取所有API接口信息"""
        logger.info("开始提取API接口信息...")
        
        # 遍历所有模块
        modules = ['payment', 'invoice', 'adjust', 'penalty', 'reminder', 'supplementary']
        
        for module in modules:
            module_path = self.controller_path / module / "controller"
            if module_path.exists():
                logger.info(f"处理模块: {module}")
                self._process_module(module, module_path)
        
        logger.info(f"总共提取到 {len(self.apis)} 个接口")
        return self.apis
    
    def _process_module(self, module_name, module_path):
        """处理单个模块的Controller文件"""
        for java_file in module_path.glob("*.java"):
            if "Controller" in java_file.name:
                logger.info(f"处理文件: {java_file.name}")
                self._parse_controller_file(module_name, java_file)
    
    def _parse_controller_file(self, module_name, file_path):
        """解析Controller文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 提取类级别信息
            class_info = self._extract_class_info(content)
            class_name = file_path.stem
            
            # 提取方法级别信息
            methods = self._extract_methods(content)
            
            for method in methods:
                api_info = {
                    '模块': module_name,
                    '控制器类': class_name,
                    '类描述': class_info.get('description', ''),
                    '类路径': class_info.get('request_mapping', ''),
                    '接口名称': method.get('method_name', ''),
                    'HTTP方法': method.get('http_method', ''),
                    '接口路径': method.get('path', ''),
                    '完整路径': self._build_full_path(class_info.get('request_mapping', ''), method.get('path', '')),
                    '接口说明': method.get('api_operation_value', ''),
                    '接口描述': method.get('api_operation_notes', ''),
                    '作者': method.get('author', ''),
                    '是否隐藏': method.get('hidden', 'false'),
                    '参数': method.get('parameters', ''),
                    '返回类型': method.get('return_type', '')
                }
                self.apis.append(api_info)
                
        except Exception as e:
            logger.error(f"解析文件 {file_path} 时出错: {e}")
    
    def _extract_class_info(self, content):
        """提取类级别的注解信息"""
        class_info = {}
        
        # 提取@RequestMapping
        request_mapping_pattern = r'@RequestMapping\s*\(\s*["\']([^"\']+)["\']'
        match = re.search(request_mapping_pattern, content)
        if match:
            class_info['request_mapping'] = match.group(1)
        
        # 提取@Api注解
        api_pattern = r'@Api\s*\(\s*tags\s*=\s*["\']([^"\']+)["\']'
        match = re.search(api_pattern, content)
        if match:
            class_info['description'] = match.group(1)
            
        return class_info
    
    def _extract_methods(self, content):
        """提取方法信息"""
        methods = []
        
        # 匹配方法模式 - 更复杂的正则表达式来匹配完整的方法定义
        method_pattern = r'(@\w+.*?)\s*public\s+[\w<>?,\s]+\s+(\w+)\s*\([^)]*\)\s*\{'
        
        # 先找到所有方法的位置
        method_matches = list(re.finditer(method_pattern, content, re.DOTALL))
        
        for i, match in enumerate(method_matches):
            method_start = match.start()
            # 找到下一个方法的开始位置，或者文件结束
            if i + 1 < len(method_matches):
                method_end = method_matches[i + 1].start()
            else:
                method_end = len(content)
            
            method_content = content[method_start:method_end]
            method_info = self._parse_method_annotations(method_content)
            method_info['method_name'] = match.group(2)
            
            # 只处理有HTTP映射注解的方法
            if method_info.get('http_method'):
                methods.append(method_info)
        
        return methods
    
    def _parse_method_annotations(self, method_content):
        """解析方法的注解"""
        method_info = {}

        # HTTP方法映射
        http_mappings = {
            'GetMapping': 'GET',
            'PostMapping': 'POST',
            'PutMapping': 'PUT',
            'DeleteMapping': 'DELETE',
            'RequestMapping': 'REQUEST'
        }

        # 提取HTTP方法和路径
        for annotation, http_method in http_mappings.items():
            # 匹配 value = "path" 形式
            pattern = rf'@{annotation}\s*\(\s*value\s*=\s*["\']([^"\']+)["\']'
            match = re.search(pattern, method_content)
            if match:
                method_info['http_method'] = http_method
                method_info['path'] = match.group(1)
                break

            # 匹配简化形式 @Mapping("path")
            pattern = rf'@{annotation}\s*\(\s*["\']([^"\']+)["\']'
            match = re.search(pattern, method_content)
            if match:
                method_info['http_method'] = http_method
                method_info['path'] = match.group(1)
                break

            # 匹配无参数形式 @Mapping
            pattern = rf'@{annotation}\s*(?:\(\s*\))?'
            match = re.search(pattern, method_content)
            if match:
                method_info['http_method'] = http_method
                method_info['path'] = ''
                break

        # 提取@ApiOperation - 支持多行和复杂格式
        api_op_patterns = [
            r'@ApiOperation\s*\(\s*value\s*=\s*["\']([^"\']*)["\'](?:,\s*notes\s*=\s*["\']([^"\']*)["\'])?',
            r'@ApiOperation\s*\(\s*value\s*=\s*["\']([^"\']*)["\'][^)]*notes\s*=\s*["\']([^"\']*)["\']',
            r'@ApiOperation\s*\([^)]*value\s*=\s*["\']([^"\']*)["\']'
        ]

        for pattern in api_op_patterns:
            match = re.search(pattern, method_content, re.DOTALL)
            if match:
                method_info['api_operation_value'] = match.group(1)
                if len(match.groups()) > 1 and match.group(2):
                    method_info['api_operation_notes'] = match.group(2)
                break

        # 提取作者信息
        author_pattern = r'@ApiOperationSupport\s*\([^)]*author\s*=\s*["\']([^"\']+)["\']'
        match = re.search(author_pattern, method_content)
        if match:
            method_info['author'] = match.group(1)

        # 提取hidden信息
        hidden_pattern = r'hidden\s*=\s*(true|false)'
        match = re.search(hidden_pattern, method_content)
        if match:
            method_info['hidden'] = match.group(1)

        # 提取参数信息
        param_pattern = r'@RequestParam[^)]*|@RequestBody[^)]*|@PathVariable[^)]*'
        params = re.findall(param_pattern, method_content)
        if params:
            method_info['parameters'] = '; '.join(params)

        return method_info
    
    def _build_full_path(self, class_path, method_path):
        """构建完整的API路径"""
        if not class_path:
            return method_path
        if not method_path:
            return class_path
        
        # 确保路径格式正确
        class_path = class_path.strip('/')
        method_path = method_path.strip('/')
        
        if method_path:
            return f"/{class_path}/{method_path}"
        else:
            return f"/{class_path}"
    
    def export_to_excel(self, output_file="api_interfaces.xlsx"):
        """导出到Excel文件"""
        if not self.apis:
            logger.warning("没有找到API接口信息")
            return
        
        # 创建DataFrame
        df = pd.DataFrame(self.apis)
        
        # 按模块和控制器类排序
        df = df.sort_values(['模块', '控制器类', '接口名称'])
        
        # 导出到Excel
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='API接口列表', index=False)
            
            # 调整列宽
            worksheet = writer.sheets['API接口列表']
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                worksheet.column_dimensions[column_letter].width = adjusted_width
        
        logger.info(f"API接口信息已导出到: {output_file}")
        logger.info(f"总共导出 {len(self.apis)} 个接口")

def main():
    """主函数"""
    # 项目根目录
    project_root = "."
    
    # 创建提取器
    extractor = APIExtractor(project_root)
    
    # 提取API信息
    apis = extractor.extract_apis()
    
    # 导出到Excel
    extractor.export_to_excel("项目接口信息.xlsx")
    
    print(f"接口提取完成！共提取到 {len(apis)} 个接口")
    print("Excel文件已生成：项目接口信息.xlsx")

if __name__ == "__main__":
    main()
