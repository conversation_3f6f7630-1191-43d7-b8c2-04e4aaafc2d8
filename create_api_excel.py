#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# 基于已收集的信息手动创建接口列表
api_data = [
    # Payment模块
    {"模块": "payment", "控制器类": "PreviewController", "类描述": "预览模块", "类路径": "/v2/priview", "接口名称": "getPreviewBills", "HTTP方法": "POST", "接口路径": "/getPreviewBills", "完整路径": "/v2/priview/getPreviewBills", "接口说明": "3.39. 商业合同账单预览接口", "接口描述": "3.39. 商业合同账单预览接口", "作者": "姚春雨"},
    
    {"模块": "payment", "控制器类": "BbpmBillManagementBusinessController", "类描述": "账单管理(来源业财)v3.0", "类路径": "/v2/business/bbpmBillManagementEntity", "接口名称": "selectByPageRecord", "HTTP方法": "GET", "接口路径": "/selectByPage", "完整路径": "/v2/business/bbpmBillManagementEntity/selectByPage", "接口说明": "账单管理--分页查询", "接口描述": "账单管理--分页查询", "作者": "binghong.tang"},
    
    {"模块": "payment", "控制器类": "BbpmBillManagementBusinessController", "类描述": "账单管理(来源业财)v3.0", "类路径": "/v2/business/bbpmBillManagementEntity", "接口名称": "getBillList", "HTTP方法": "GET", "接口路径": "/getBillList", "完整路径": "/v2/business/bbpmBillManagementEntity/getBillList", "接口说明": "账单管理--查询全部", "接口描述": "账单管理--查询全部", "作者": "binghong.tang"},
    
    {"模块": "payment", "控制器类": "BbpmBillManagementBusinessController", "类描述": "账单管理(来源业财)v3.0", "类路径": "/v2/business/bbpmBillManagementEntity", "接口名称": "transferInfo", "HTTP方法": "GET", "接口路径": "/transferInfo", "完整路径": "/v2/business/bbpmBillManagementEntity/transferInfo", "接口说明": "查询线下转账信息", "接口描述": "查询线下转账信息", "作者": "binghong.tang"},
    
    {"模块": "payment", "控制器类": "BbpmBillManagementBusinessController", "类描述": "账单管理(来源业财)v3.0", "类路径": "/v2/business/bbpmBillManagementEntity", "接口名称": "queryMonthBillList", "HTTP方法": "GET", "接口路径": "/queryMonthBillList", "完整路径": "/v2/business/bbpmBillManagementEntity/queryMonthBillList", "接口说明": "3.60.查询月度账单接口", "接口描述": "账单管理--分页查询", "作者": "binghong.tang"},
    
    {"模块": "payment", "控制器类": "BbpmCollectionBusinessController", "类描述": "收款表(部分来源业财)v3.0", "类路径": "/v2/business/bbpmCollectionEntity", "接口名称": "selectByPageRecord", "HTTP方法": "GET", "接口路径": "/selectByPage", "完整路径": "/v2/business/bbpmCollectionEntity/selectByPage", "接口说明": "收款单管理--分页查询", "接口描述": "收款单管理--分页查询", "作者": "binghong.tang"},
    
    {"模块": "payment", "控制器类": "BbpmCollectionBusinessController", "类描述": "收款表(部分来源业财)v3.0", "类路径": "/v2/business/bbpmCollectionEntity", "接口名称": "getReceiptList", "HTTP方法": "GET", "接口路径": "/getReceiptList", "完整路径": "/v2/business/bbpmCollectionEntity/getReceiptList", "接口说明": "收款单管理--查询全部", "接口描述": "收款单管理--查询全部", "作者": ""},
    
    {"模块": "payment", "控制器类": "BbpmCollectionBusinessController", "类描述": "收款表(部分来源业财)v3.0", "类路径": "/v2/business/bbpmCollectionEntity", "接口名称": "chargeMoneyTotal", "HTTP方法": "GET", "接口路径": "/chargeMoneyTotal", "完整路径": "/v2/business/bbpmCollectionEntity/chargeMoneyTotal", "接口说明": "3.11.1收款单查询统计接口", "接口描述": "3.11.1收款单查询统计接口", "作者": "binghong.tang"},
    
    {"模块": "payment", "控制器类": "PayController", "类描述": "1、支付模块", "类路径": "/pay", "接口名称": "pay", "HTTP方法": "POST", "接口路径": "", "完整路径": "/pay", "接口说明": "1.1、支付", "接口描述": "支付", "作者": "姚春雨"},
    
    {"模块": "payment", "控制器类": "PayController", "类描述": "1、支付模块", "类路径": "/pay", "接口名称": "payUnlock", "HTTP方法": "POST", "接口路径": "unlock", "完整路径": "/pay/unlock", "接口说明": "1.2、支付解锁", "接口描述": "移动端调整到第三方平台进行支付，跳转到第三方平台后未支付，返回到我们应用的时候通知工银解锁支付状态。", "作者": "姚春雨"},
    
    {"模块": "payment", "控制器类": "BbpDictController", "类描述": "字典表", "类路径": "/v2/atomic/bbpDictEntity", "接口名称": "selectByDictCode", "HTTP方法": "GET", "接口路径": "/selectByDictCode", "完整路径": "/v2/atomic/bbpDictEntity/selectByDictCode", "接口说明": "根据字典code查询code对应字典（统一返回报文）", "接口描述": "根据字典code查询code对应字典", "作者": ""},
    
    {"模块": "payment", "控制器类": "BbpmCashPledgeController", "类描述": "押金条", "类路径": "/v2/atomic/bbpmCashPledgeEntity", "接口名称": "selectByPageRecord", "HTTP方法": "GET", "接口路径": "/selectByPage", "完整路径": "/v2/atomic/bbpmCashPledgeEntity/selectByPage", "接口说明": "分页查询", "接口描述": "分页查询", "作者": "binghong.tang"},
    
    {"模块": "payment", "控制器类": "BbpmCashPledgeBusinessController", "类描述": "押金条", "类路径": "/v2/business/bbpmCashPledgeEntity", "接口名称": "selectByList", "HTTP方法": "GET", "接口路径": "/selectByList", "完整路径": "/v2/business/bbpmCashPledgeEntity/selectByList", "接口说明": "查询押金条列表", "接口描述": "查询押金条列表", "作者": "binghong.tang"},
    
    {"模块": "payment", "控制器类": "BbpmWithholdBusinessController", "类描述": "代扣业务", "类路径": "/v2/business/bbpmWithholdEntity", "接口名称": "detail", "HTTP方法": "GET", "接口路径": "/detail", "完整路径": "/v2/business/bbpmWithholdEntity/detail", "接口说明": "3.21.4手动代扣-报盘明细查询", "接口描述": "3.21.4手动代扣-报盘明细查询", "作者": "binghong.tang"},
    
    {"模块": "payment", "控制器类": "BillFivePhaseController", "类描述": "五期合同", "类路径": "/v2/business/billFivePhaseEntity", "接口名称": "agreementPhase", "HTTP方法": "POST", "接口路径": "/agreementPhase", "完整路径": "/v2/business/billFivePhaseEntity/agreementPhase", "接口说明": "管理协议候审期生成账单", "接口描述": "管理协议候审期生成账单", "作者": "gxp"},
    
    {"模块": "payment", "控制器类": "BillFivePhaseController", "类描述": "五期合同", "类路径": "/v2/business/billFivePhaseEntity", "接口名称": "singlePhase", "HTTP方法": "POST", "接口路径": "/singlePhase", "完整路径": "/v2/business/billFivePhaseEntity/singlePhase", "接口说明": "趸租大合同候审期生成账单", "接口描述": "趸租大合同候审期生成账单", "作者": "gxp"},
    
    # Invoice模块
    {"模块": "invoice", "控制器类": "BbpmInvoiceHeaderController", "类描述": "常用发票抬头信息表", "类路径": "/v2/atomic/bbpmInvoiceHeaderEntity", "接口名称": "insertRecord", "HTTP方法": "POST", "接口路径": "/insertRecord", "完整路径": "/v2/atomic/bbpmInvoiceHeaderEntity/insertRecord", "接口说明": "新增", "接口描述": "新增全表数据", "作者": "binghong.tang"},
    
    {"模块": "invoice", "控制器类": "BbpmInvoiceHeaderController", "类描述": "常用发票抬头信息表", "类路径": "/v2/atomic/bbpmInvoiceHeaderEntity", "接口名称": "updateByIdRecord", "HTTP方法": "POST", "接口路径": "/updateById", "完整路径": "/v2/atomic/bbpmInvoiceHeaderEntity/updateById", "接口说明": "根据主键更新", "接口描述": "根据主键更新表中信息 更新全部信息", "作者": "binghong.tang"},
    
    {"模块": "invoice", "控制器类": "BbpmInvoiceHeaderController", "类描述": "常用发票抬头信息表", "类路径": "/v2/atomic/bbpmInvoiceHeaderEntity", "接口名称": "selectByPageRecord", "HTTP方法": "GET", "接口路径": "/selectByPage", "完整路径": "/v2/atomic/bbpmInvoiceHeaderEntity/selectByPage", "接口说明": "分页查询", "接口描述": "分页查询", "作者": "binghong.tang"},
    
    {"模块": "invoice", "控制器类": "BbpmInvoiceQueryBusinessController", "类描述": "已开发票查询", "类路径": "/v2/business/bbpmInvoiceQueryEntity", "接口名称": "selectByPageRecord", "HTTP方法": "GET", "接口路径": "/selectByPage", "完整路径": "/v2/business/bbpmInvoiceQueryEntity/selectByPage", "接口说明": "已开票--分页查询", "接口描述": "分页查询", "作者": "binghong.tang"},
    
    {"模块": "invoice", "控制器类": "BbpmInvoiceQueryBusinessController", "类描述": "已开发票查询", "类路径": "/v2/business/bbpmInvoiceQueryEntity", "接口名称": "selectByIdRecord", "HTTP方法": "GET", "接口路径": "/selectById", "完整路径": "/v2/business/bbpmInvoiceQueryEntity/selectById", "接口说明": "已开票--根据主键查询", "接口描述": "根据主键查询表中信息", "作者": "binghong.tang"},
    
    {"模块": "invoice", "控制器类": "BbpmInvoiceIssueBusinessController", "类描述": "开具发票", "类路径": "/v2/business/bbpmInvoiceIssueEntity", "接口名称": "issueInvoice", "HTTP方法": "POST", "接口路径": "/issueInvoice", "完整路径": "/v2/business/bbpmInvoiceIssueEntity/issueInvoice", "接口说明": "开具发票", "接口描述": "开具发票", "作者": "binghong.tang"},
    
    {"模块": "invoice", "控制器类": "BbpmInvoiceIssueBusinessController", "类描述": "开具发票", "类路径": "/v2/business/bbpmInvoiceIssueEntity", "接口名称": "redFlushBatch", "HTTP方法": "POST", "接口路径": "/redFlushBatch", "完整路径": "/v2/business/bbpmInvoiceIssueEntity/redFlushBatch", "接口说明": "批量发票红冲", "接口描述": "发票信息", "作者": "binghong.tang"},
    
    {"模块": "invoice", "控制器类": "BbpmInvoiceIssueBusinessController", "类描述": "开具发票", "类路径": "/v2/business/bbpmInvoiceIssueEntity", "接口名称": "pushInvoiceBatch", "HTTP方法": "POST", "接口路径": "/pushInvoiceBatch", "完整路径": "/v2/business/bbpmInvoiceIssueEntity/pushInvoiceBatch", "接口说明": "批量发票推送", "接口描述": "推送信息", "作者": "binghong.tang"},
    
    {"模块": "invoice", "控制器类": "BbpmInvoiceIssueBusinessController", "类描述": "开具发票", "类路径": "/v2/business/bbpmInvoiceIssueEntity", "接口名称": "redFlushAndIssue", "HTTP方法": "POST", "接口路径": "/redFlushAndIssue", "完整路径": "/v2/business/bbpmInvoiceIssueEntity/redFlushAndIssue", "接口说明": "app重开发票", "接口描述": "新增全表数据", "作者": "binghong.tang"},
    
    # Supplementary模块
    {"模块": "supplementary", "控制器类": "BbpmBusinessSupplementaryInfoController", "类描述": "追加单业务", "类路径": "/v2/business/bbpmSupplementaryInfoEntity", "接口名称": "selectByPageRecord", "HTTP方法": "GET", "接口路径": "/selectByPage", "完整路径": "/v2/business/bbpmSupplementaryInfoEntity/selectByPage", "接口说明": "分页查询", "接口描述": "分页查询", "作者": "pyj"},
    
    {"模块": "supplementary", "控制器类": "BbpmBusinessSupplementaryInfoController", "类描述": "追加单业务", "类路径": "/v2/business/bbpmSupplementaryInfoEntity", "接口名称": "operateDetailInfo", "HTTP方法": "GET", "接口路径": "/operateDetailInfo", "完整路径": "/v2/business/bbpmSupplementaryInfoEntity/operateDetailInfo", "接口说明": "操作记录", "接口描述": "追加单审批信息统计", "作者": "pyj"},
    
    # Reminder模块
    {"模块": "reminder", "控制器类": "BbpmReminderRulesMainController", "类描述": "缴费提醒规则--主表", "类路径": "/v2/atomic/bbpmReminderRulesMainEntity", "接口名称": "selectByPageRecord", "HTTP方法": "GET", "接口路径": "/selectByPage", "完整路径": "/v2/atomic/bbpmReminderRulesMainEntity/selectByPage", "接口说明": "分页查询", "接口描述": "分页查询", "作者": "binghong.tang"},
    
    {"模块": "reminder", "控制器类": "BbpmCollectionWhitelistController", "类描述": "催缴白名单表", "类路径": "/v2/atomic/bbpmCollectionWhitelistEntity", "接口名称": "selectByPageRecord", "HTTP方法": "GET", "接口路径": "/selectByPage", "完整路径": "/v2/atomic/bbpmCollectionWhitelistEntity/selectByPage", "接口说明": "分页查询", "接口描述": "分页查询", "作者": "binghong.tang"},
    
    # Adjust模块
    {"模块": "adjust", "控制器类": "BbpmReceivableAdjustController", "类描述": "应收调整", "类路径": "/v2/business/bbpmReceivableAdjustEntity", "接口名称": "selectByPageRecord", "HTTP方法": "GET", "接口路径": "/selectByPage", "完整路径": "/v2/business/bbpmReceivableAdjustEntity/selectByPage", "接口说明": "分页查询", "接口描述": "分页查询", "作者": "yuanxuesong"},
    
    {"模块": "adjust", "控制器类": "BbpmReceivableAdjustBillController", "类描述": "应收调整账单表", "类路径": "/bbpmReceivableAdjustBillEntity", "接口名称": "selectByPageRecord", "HTTP方法": "GET", "接口路径": "/selectByPage", "完整路径": "/bbpmReceivableAdjustBillEntity/selectByPage", "接口说明": "分页查询", "接口描述": "分页查询", "作者": "yuanxuesong"},
    
    {"模块": "adjust", "控制器类": "BbpmReceivableAdjustExamineController", "类描述": "应收调整审核表", "类路径": "/bbpmReceivableAdjustExamineEntity", "接口名称": "selectByPageRecord", "HTTP方法": "GET", "接口路径": "/selectByPage", "完整路径": "/bbpmReceivableAdjustExamineEntity/selectByPage", "接口说明": "分页查询", "接口描述": "分页查询", "作者": "yuanxuesong"},
]

# 创建CSV文件
import csv

with open('项目接口信息.csv', 'w', newline='', encoding='utf-8-sig') as csvfile:
    fieldnames = ['模块', '控制器类', '类描述', '类路径', '接口名称', 'HTTP方法', '接口路径', '完整路径', '接口说明', '接口描述', '作者']
    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
    
    writer.writeheader()
    for api in api_data:
        writer.writerow(api)

print(f"接口信息已导出到CSV文件：项目接口信息.csv")
print(f"总共导出 {len(api_data)} 个接口")
