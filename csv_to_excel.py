#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd

# 读取CSV文件
df = pd.read_csv('项目接口信息.csv', encoding='utf-8')

# 导出为Excel文件
with pd.ExcelWriter('项目接口信息.xlsx', engine='openpyxl') as writer:
    df.to_excel(writer, sheet_name='API接口列表', index=False)
    
    # 调整列宽
    worksheet = writer.sheets['API接口列表']
    for column in worksheet.columns:
        max_length = 0
        column_letter = column[0].column_letter
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = min(max_length + 2, 50)
        worksheet.column_dimensions[column_letter].width = adjusted_width

print("CSV文件已成功转换为Excel文件：项目接口信息.xlsx")
print(f"总共包含 {len(df)} 个接口")
