#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版API接口信息提取脚本
"""

import os
import re
import csv
from pathlib import Path

def extract_controller_info(file_path):
    """提取单个Controller文件的信息"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
    except:
        return []
    
    apis = []
    
    # 提取类级别信息
    class_request_mapping = ""
    class_description = ""
    
    # 提取@RequestMapping
    request_mapping_match = re.search(r'@RequestMapping\s*\(\s*["\']([^"\']+)["\']', content)
    if request_mapping_match:
        class_request_mapping = request_mapping_match.group(1)
    
    # 提取@Api
    api_match = re.search(r'@Api\s*\(\s*tags\s*=\s*["\']([^"\']+)["\']', content)
    if api_match:
        class_description = api_match.group(1)
    
    # 提取所有方法
    # 查找所有包含HTTP映射注解的方法
    http_patterns = [
        (r'@GetMapping\s*\(\s*value\s*=\s*["\']([^"\']+)["\']', 'GET'),
        (r'@GetMapping\s*\(\s*["\']([^"\']+)["\']', 'GET'),
        (r'@PostMapping\s*\(\s*value\s*=\s*["\']([^"\']+)["\']', 'POST'),
        (r'@PostMapping\s*\(\s*["\']([^"\']+)["\']', 'POST'),
        (r'@PutMapping\s*\(\s*value\s*=\s*["\']([^"\']+)["\']', 'PUT'),
        (r'@PutMapping\s*\(\s*["\']([^"\']+)["\']', 'PUT'),
        (r'@DeleteMapping\s*\(\s*value\s*=\s*["\']([^"\']+)["\']', 'DELETE'),
        (r'@DeleteMapping\s*\(\s*["\']([^"\']+)["\']', 'DELETE'),
    ]
    
    for pattern, http_method in http_patterns:
        matches = re.finditer(pattern, content)
        for match in matches:
            method_path = match.group(1)
            
            # 查找这个映射注解后面的方法定义和ApiOperation
            start_pos = match.end()
            method_section = content[start_pos:start_pos+2000]  # 取后面2000个字符
            
            # 提取ApiOperation
            api_operation = ""
            api_notes = ""
            author = ""
            
            api_op_match = re.search(r'@ApiOperation\s*\(\s*value\s*=\s*["\']([^"\']*)["\']', method_section)
            if api_op_match:
                api_operation = api_op_match.group(1)
            
            notes_match = re.search(r'notes\s*=\s*["\']([^"\']*)["\']', method_section)
            if notes_match:
                api_notes = notes_match.group(1)
            
            author_match = re.search(r'author\s*=\s*["\']([^"\']*)["\']', method_section)
            if author_match:
                author = author_match.group(1)
            
            # 提取方法名
            method_name_match = re.search(r'public\s+[\w<>?,\s]+\s+(\w+)\s*\(', method_section)
            method_name = method_name_match.group(1) if method_name_match else ""
            
            # 构建完整路径
            full_path = class_request_mapping
            if method_path and method_path != "/":
                if not full_path.endswith("/") and not method_path.startswith("/"):
                    full_path += "/"
                full_path += method_path.lstrip("/")
            
            apis.append({
                'controller_file': file_path.name,
                'class_description': class_description,
                'class_path': class_request_mapping,
                'method_name': method_name,
                'http_method': http_method,
                'method_path': method_path,
                'full_path': full_path,
                'api_operation': api_operation,
                'api_notes': api_notes,
                'author': author
            })
    
    return apis

def main():
    """主函数"""
    project_root = Path(".")
    controller_base = project_root / "src/main/java/com/bonc/ioc/bzf/business"
    
    all_apis = []
    modules = ['payment', 'invoice', 'adjust', 'penalty', 'reminder', 'supplementary']
    
    for module in modules:
        controller_path = controller_base / module / "controller"
        if controller_path.exists():
            print(f"处理模块: {module}")
            for java_file in controller_path.glob("*Controller.java"):
                print(f"  处理文件: {java_file.name}")
                apis = extract_controller_info(java_file)
                for api in apis:
                    api['module'] = module
                    all_apis.append(api)
    
    # 导出到CSV文件
    if all_apis:
        with open('项目接口信息.csv', 'w', newline='', encoding='utf-8-sig') as csvfile:
            fieldnames = ['module', 'controller_file', 'class_description', 'class_path', 
                         'method_name', 'http_method', 'method_path', 'full_path', 
                         'api_operation', 'api_notes', 'author']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            
            # 写入表头（中文）
            writer.writerow({
                'module': '模块',
                'controller_file': '控制器文件',
                'class_description': '类描述',
                'class_path': '类路径',
                'method_name': '方法名',
                'http_method': 'HTTP方法',
                'method_path': '方法路径',
                'full_path': '完整路径',
                'api_operation': '接口说明',
                'api_notes': '接口描述',
                'author': '作者'
            })
            
            # 写入数据
            for api in all_apis:
                writer.writerow(api)
        
        print(f"\n接口提取完成！")
        print(f"总共提取到 {len(all_apis)} 个接口")
        print("CSV文件已生成：项目接口信息.csv")
    else:
        print("没有找到任何接口信息")

if __name__ == "__main__":
    main()
