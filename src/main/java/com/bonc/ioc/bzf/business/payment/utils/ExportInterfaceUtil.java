package com.bonc.ioc.bzf.business.payment.utils;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.Data;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.lang.reflect.Method;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;

/**
 * test demo
 *
 * <AUTHOR>
 * @date 2021/5/20 10:35
 * @change: 2021/5/20 10:35 by jin.xu for init
 */
public class ExportInterfaceUtil {
    private static List<InterfaceInfo> interfaceInfos = new ArrayList<>();
    public static void main(String[] args) {
        try {
            String serviceName = "签约系统";

            String dir = System.getProperty("user.dir");
            System.out.println(dir);
            String path = "E:\\work\\bzf-system-signing\\src\\main\\java\\com\\bonc\\ioc\\bzf\\system\\signing\\workflow\\controller";
            System.out.println(path);
            File root = new File(path);
            findJavaFiles(root);

            File file = new File("D:\\"+serviceName+"-接口信息-"+System.currentTimeMillis()+".xlsx");
            OutputStream os = new FileOutputStream(file);
            Workbook wb = new XSSFWorkbook();
            Sheet sheet = wb.createSheet();
            for (int i = 0; i < interfaceInfos.size(); i++) {
                Row row = sheet.createRow(i);
                InterfaceInfo interfaceInfo = interfaceInfos.get(i);
                row.createCell(0).setCellValue(serviceName);
                row.createCell(1).setCellValue(interfaceInfo.getPath());
                row.createCell(2).setCellValue(interfaceInfo.getDesc());
            }

            wb.write(os);
            wb.close();
            os.close();


        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    public static void findJavaFiles(File parent) throws Exception {
        if (parent != null){
            File[] files = parent.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isDirectory()) {
                        findJavaFiles(file);
                    } else if (file.isFile() && file.getName().endsWith(".java")) {
                        handle(file.getAbsolutePath());
                    }
                }
            }
        }
    }

    /**
     * 处理文件
     * @param filePath
     * @throws IOException
     */
    public static void handle(String filePath) throws Exception {

        // 读取文件内容
        String content = new String(Files.readAllBytes(Paths.get(filePath)));

        // 获取全类名
        String fullClassName = getFullClassName(filePath,content);

        Class<?> clazz = Class.forName(fullClassName);

        String rootPath = null;
        if (clazz.isAnnotationPresent(RequestMapping.class)){
            RequestMapping requestMapping = clazz.getAnnotation(RequestMapping.class);
            String[] rootPaths = requestMapping.value();
            if (rootPaths.length > 0){
                rootPath = rootPaths[0];
            }
        }

        String rootDesc = null;
        if (clazz.isAnnotationPresent(Api.class)){
            Api api = clazz.getAnnotation(Api.class);
            String[] tags = api.tags();
            if (tags != null && tags.length > 0){
                rootDesc = tags[0];
            }
        }

        Method[] methods = clazz.getMethods();
        for (Method method : methods) {
            String methodPath = null;
            String desc = null;
            if (method.isAnnotationPresent(ApiOperation.class)){
                ApiOperation apiOperation = method.getAnnotation(ApiOperation.class);
                desc = apiOperation.value();
            }

            if (method.isAnnotationPresent(RequestMapping.class)){
                RequestMapping requestMapping = method.getAnnotation(RequestMapping.class);
                methodPath = requestMapping.value()[0];
            }else if (method.isAnnotationPresent(GetMapping.class)){
                GetMapping getMapping = method.getAnnotation(GetMapping.class);
                methodPath = getMapping.value()[0];
            }else if (method.isAnnotationPresent(PostMapping.class)){
                PostMapping postMapping = method.getAnnotation(PostMapping.class);
                methodPath = postMapping.value()[0];
            }else if (method.isAnnotationPresent(PutMapping.class)){
                PutMapping putMapping = method.getAnnotation(PutMapping.class);
                methodPath = putMapping.value()[0];
            }else if (method.isAnnotationPresent(DeleteMapping.class)){
                DeleteMapping deleteMapping = method.getAnnotation(DeleteMapping.class);
                methodPath = deleteMapping.value()[0];
            }


            if (methodPath != null){
                InterfaceInfo interfaceInfo = new InterfaceInfo();
                if (rootPath != null){
                    methodPath = rootPath+methodPath;
                }
                interfaceInfo.setPath(methodPath);

                if (desc != null){
                    if (desc.contains("、")){
                        desc = desc.substring(desc.lastIndexOf("、")+1);
                    }
                }

                if (rootDesc != null){
                    desc = rootDesc+desc;
                }
                interfaceInfo.setDesc(desc);

                interfaceInfos.add(interfaceInfo);
            }




        }



    }

    private static String getFullClassName(String filePath,String content) {
        int i1 = filePath.lastIndexOf("/");
        if (i1 == -1){
            i1 = filePath.lastIndexOf("\\");
        }
        String  javaFileName = filePath.substring(i1 +1, filePath.indexOf(".java"));
        int i = content.indexOf(";");

        String substring = content.substring(0, i);
        String[] split = substring.split(" ");
        String javaPackageName = split[split.length - 1];

        // 匹配内容

        return javaPackageName+"."+javaFileName;
    }

    @Data
    static class InterfaceInfo{
        private String path;
        private String method;
        private String desc;
    }

}
