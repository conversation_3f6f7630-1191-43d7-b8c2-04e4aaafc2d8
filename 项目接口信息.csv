模块,控制器类,类描述,类路径,接口名称,HTTP方法,接口路径,完整路径,接口说明,接口描述,作者
payment,PreviewController,预览模块,/v2/priview,getPreviewBills,POST,/getPreviewBills,/v2/priview/getPreviewBills,3.39. 商业合同账单预览接口,3.39. 商业合同账单预览接口,姚春雨
payment,BbpmBillManagementBusinessController,账单管理(来源业财)v3.0,/v2/business/bbpmBillManagementEntity,selectByPageRecord,GET,/selectByPage,/v2/business/bbpmBillManagementEntity/selectByPage,账单管理--分页查询,账单管理--分页查询,binghong.tang
payment,BbpmBillManagementBusinessController,账单管理(来源业财)v3.0,/v2/business/bbpmBillManagementEntity,getBillList,GET,/getBillList,/v2/business/bbpmBillManagementEntity/getBillList,账单管理--查询全部,账单管理--查询全部,binghong.tang
payment,BbpmBillManagementBusinessController,账单管理(来源业财)v3.0,/v2/business/bbpmBillManagementEntity,transferInfo,GET,/transferInfo,/v2/business/bbpmBillManagementEntity/transferInfo,查询线下转账信息,查询线下转账信息,binghong.tang
payment,BbpmBillManagementBusinessController,账单管理(来源业财)v3.0,/v2/business/bbpmBillManagementEntity,queryMonthBillList,GET,/queryMonthBillList,/v2/business/bbpmBillManagementEntity/queryMonthBillList,3.60.查询月度账单接口,账单管理--分页查询,binghong.tang
payment,BbpmCollectionBusinessController,收款表(部分来源业财)v3.0,/v2/business/bbpmCollectionEntity,selectByPageRecord,GET,/selectByPage,/v2/business/bbpmCollectionEntity/selectByPage,收款单管理--分页查询,收款单管理--分页查询,binghong.tang
payment,BbpmCollectionBusinessController,收款表(部分来源业财)v3.0,/v2/business/bbpmCollectionEntity,getReceiptList,GET,/getReceiptList,/v2/business/bbpmCollectionEntity/getReceiptList,收款单管理--查询全部,收款单管理--查询全部,
payment,BbpmCollectionBusinessController,收款表(部分来源业财)v3.0,/v2/business/bbpmCollectionEntity,chargeMoneyTotal,GET,/chargeMoneyTotal,/v2/business/bbpmCollectionEntity/chargeMoneyTotal,3.11.1收款单查询统计接口,3.11.1收款单查询统计接口,binghong.tang
payment,PayController,1、支付模块,/pay,pay,POST,,/pay,1.1、支付,支付,姚春雨
payment,PayController,1、支付模块,/pay,payUnlock,POST,unlock,/pay/unlock,1.2、支付解锁,移动端调整到第三方平台进行支付跳转到第三方平台后未支付返回到我们应用的时候通知工银解锁支付状态,姚春雨
payment,BbpDictController,字典表,/v2/atomic/bbpDictEntity,selectByDictCode,GET,/selectByDictCode,/v2/atomic/bbpDictEntity/selectByDictCode,根据字典code查询code对应字典（统一返回报文）,根据字典code查询code对应字典,
payment,BbpmCashPledgeController,押金条,/v2/atomic/bbpmCashPledgeEntity,selectByPageRecord,GET,/selectByPage,/v2/atomic/bbpmCashPledgeEntity/selectByPage,分页查询,分页查询,binghong.tang
payment,BbpmCashPledgeBusinessController,押金条,/v2/business/bbpmCashPledgeEntity,selectByList,GET,/selectByList,/v2/business/bbpmCashPledgeEntity/selectByList,查询押金条列表,查询押金条列表,binghong.tang
payment,BbpmWithholdBusinessController,代扣业务,/v2/business/bbpmWithholdEntity,detail,GET,/detail,/v2/business/bbpmWithholdEntity/detail,3.21.4手动代扣-报盘明细查询,3.21.4手动代扣-报盘明细查询,binghong.tang
payment,BillFivePhaseController,五期合同,/v2/business/billFivePhaseEntity,agreementPhase,POST,/agreementPhase,/v2/business/billFivePhaseEntity/agreementPhase,管理协议候审期生成账单,管理协议候审期生成账单,gxp
payment,BillFivePhaseController,五期合同,/v2/business/billFivePhaseEntity,singlePhase,POST,/singlePhase,/v2/business/billFivePhaseEntity/singlePhase,趸租大合同候审期生成账单,趸租大合同候审期生成账单,gxp
payment,BbpmPayeeBusinessController,收款人管理,/v2/business/bbpmPayeeEntity,selectByPageRecord,GET,/selectByPage,/v2/business/bbpmPayeeEntity/selectByPage,分页查询,分页查询,binghong.tang
payment,BbsiRuleInfoBusinessController,银行规则信息,/v2/business/bbsiRuleInfoEntity,getListBankBranchCode,GET,/getListBankBranchCode,/v2/business/bbsiRuleInfoEntity/getListBankBranchCode,根据项目id获取银行,根据项目id获取银行,ly
payment,BbpmWithholdRecordController,代扣记录,/v2/atomic/bbpmWithholdRecordEntity,selectByPageRecord,GET,/selectByPage,/v2/atomic/bbpmWithholdRecordEntity/selectByPage,分页查询,分页查询,binghong.tang
payment,BbpmCashCollectionVoucherController,现金收缴凭证表v3.0,/v2/atomic/bbpmCashCollectionVoucherEntity,selectByPageRecord,GET,/selectByPage,/v2/atomic/bbpmCashCollectionVoucherEntity/selectByPage,分页查询,分页查询,binghong.tang
payment,BbpmChangeContractController,合同变更,/v2/atomic/bbpmChangeContractEntity,selectByPageRecord,GET,/selectByPage,/v2/atomic/bbpmChangeContractEntity/selectByPage,分页查询,分页查询,binghong.tang
payment,BbpmDepositSlipBusinessController,押金条业务,/v2/business/bbpmDepositSlipEntity,selectByPageRecord,GET,/selectByPage,/v2/business/bbpmDepositSlipEntity/selectByPage,分页查询,分页查询,binghong.tang
payment,BbpmMainLesseeExcelController,主承租人Excel,/v2/business/bbpmMainLesseeExcelEntity,selectByPageRecord,GET,/selectByPage,/v2/business/bbpmMainLesseeExcelEntity/selectByPage,分页查询,分页查询,binghong.tang
payment,BbsiTryServiceFeeBusinessController,试算服务费业务,/v2/business/bbsiTryServiceFeeEntity,selectByPageRecord,GET,/selectByPage,/v2/business/bbsiTryServiceFeeEntity/selectByPage,分页查询,分页查询,binghong.tang
payment,OfferBusinessController,报盘记录,/v2/business/offerEntity,selectByPageRecord,GET,/selectByPage,/v2/business/offerEntity/selectByPage,分页查询,分页查询,binghong.tang
payment,RefreshConfigController,配置刷新,/v2/refresh,refreshConfig,POST,/config,/v2/refresh/config,刷新配置,刷新配置,
payment,WhiteListController,白名单管理,/v2/whitelist,selectByPageRecord,GET,/selectByPage,/v2/whitelist/selectByPage,分页查询,分页查询,
invoice,BbpmInvoiceHeaderController,常用发票抬头信息表,/v2/atomic/bbpmInvoiceHeaderEntity,insertRecord,POST,/insertRecord,/v2/atomic/bbpmInvoiceHeaderEntity/insertRecord,新增,新增全表数据,binghong.tang
invoice,BbpmInvoiceHeaderController,常用发票抬头信息表,/v2/atomic/bbpmInvoiceHeaderEntity,insertBatchRecord,POST,/batch/insertRecord,/v2/atomic/bbpmInvoiceHeaderEntity/batch/insertRecord,批量新增,批量新增全表数据,binghong.tang
invoice,BbpmInvoiceHeaderController,常用发票抬头信息表,/v2/atomic/bbpmInvoiceHeaderEntity,updateByIdRecord,POST,/updateById,/v2/atomic/bbpmInvoiceHeaderEntity/updateById,根据主键更新,根据主键更新表中信息 更新全部信息,binghong.tang
invoice,BbpmInvoiceHeaderController,常用发票抬头信息表,/v2/atomic/bbpmInvoiceHeaderEntity,saveByIdRecord,POST,/saveById,/v2/atomic/bbpmInvoiceHeaderEntity/saveById,根据主键更新或新增,根据主键更新或新增 根据主键查询 如果存在 则更新 如果不存在则新增,binghong.tang
invoice,BbpmInvoiceHeaderController,常用发票抬头信息表,/v2/atomic/bbpmInvoiceHeaderEntity,selectByPageRecord,GET,/selectByPage,/v2/atomic/bbpmInvoiceHeaderEntity/selectByPage,分页查询,分页查询,binghong.tang
invoice,BbpmInvoiceQueryBusinessController,已开发票查询,/v2/business/bbpmInvoiceQueryEntity,selectByPageRecord,GET,/selectByPage,/v2/business/bbpmInvoiceQueryEntity/selectByPage,已开票--分页查询,分页查询,binghong.tang
invoice,BbpmInvoiceQueryBusinessController,已开发票查询,/v2/business/bbpmInvoiceQueryEntity,selectByIdRecord,GET,/selectById,/v2/business/bbpmInvoiceQueryEntity/selectById,已开票--根据主键查询,根据主键查询表中信息,binghong.tang
invoice,BbpmInvoiceIssueBusinessController,开具发票,/v2/business/bbpmInvoiceIssueEntity,issueInvoice,POST,/issueInvoice,/v2/business/bbpmInvoiceIssueEntity/issueInvoice,开具发票,开具发票,binghong.tang
invoice,BbpmInvoiceIssueBusinessController,开具发票,/v2/business/bbpmInvoiceIssueEntity,redFlushBatch,POST,/redFlushBatch,/v2/business/bbpmInvoiceIssueEntity/redFlushBatch,批量发票红冲,发票信息,binghong.tang
invoice,BbpmInvoiceIssueBusinessController,开具发票,/v2/business/bbpmInvoiceIssueEntity,pushInvoiceBatch,POST,/pushInvoiceBatch,/v2/business/bbpmInvoiceIssueEntity/pushInvoiceBatch,批量发票推送,推送信息,binghong.tang
invoice,BbpmInvoiceIssueBusinessController,开具发票,/v2/business/bbpmInvoiceIssueEntity,redFlushAndIssue,POST,/redFlushAndIssue,/v2/business/bbpmInvoiceIssueEntity/redFlushAndIssue,app重开发票,新增全表数据,binghong.tang
supplementary,BbpmBusinessSupplementaryInfoController,追加单业务,/v2/business/bbpmSupplementaryInfoEntity,selectByPageRecord,GET,/selectByPage,/v2/business/bbpmSupplementaryInfoEntity/selectByPage,分页查询,分页查询,pyj
supplementary,BbpmBusinessSupplementaryInfoController,追加单业务,/v2/business/bbpmSupplementaryInfoEntity,operateDetailInfo,GET,/operateDetailInfo,/v2/business/bbpmSupplementaryInfoEntity/operateDetailInfo,操作记录,追加单审批信息统计,pyj
supplementary,BbpmSupplementaryInfoController,追加单信息,/v2/atomic/bbpmSupplementaryInfoEntity,selectByPageRecord,GET,/selectByPage,/v2/atomic/bbpmSupplementaryInfoEntity/selectByPage,分页查询,分页查询,pyj
supplementary,BbpmApproveInfoController,审批信息,/v2/atomic/bbpmApproveInfoEntity,selectByPageRecord,GET,/selectByPage,/v2/atomic/bbpmApproveInfoEntity/selectByPage,分页查询,分页查询,pyj
supplementary,BbpmApproveDetailInfoController,审批详细信息,/v2/atomic/bbpmApproveDetailInfoEntity,selectByPageRecord,GET,/selectByPage,/v2/atomic/bbpmApproveDetailInfoEntity/selectByPage,分页查询,分页查询,pyj
supplementary,BbpmSupplementaryPaymentController,追加单支付,/v2/atomic/bbpmSupplementaryPaymentEntity,selectByPageRecord,GET,/selectByPage,/v2/atomic/bbpmSupplementaryPaymentEntity/selectByPage,分页查询,分页查询,pyj
supplementary,BbpmSupplementaryPaymentProductController,追加单支付产品,/v2/atomic/bbpmSupplementaryPaymentProductEntity,selectByPageRecord,GET,/selectByPage,/v2/atomic/bbpmSupplementaryPaymentProductEntity/selectByPage,分页查询,分页查询,pyj
reminder,BbpmReminderRulesMainController,缴费提醒规则--主表,/v2/atomic/bbpmReminderRulesMainEntity,selectByPageRecord,GET,/selectByPage,/v2/atomic/bbpmReminderRulesMainEntity/selectByPage,分页查询,分页查询,binghong.tang
reminder,BbpmCollectionWhitelistController,催缴白名单表,/v2/atomic/bbpmCollectionWhitelistEntity,selectByPageRecord,GET,/selectByPage,/v2/atomic/bbpmCollectionWhitelistEntity/selectByPage,分页查询,分页查询,binghong.tang
reminder,BbpmMessageSendSubLogV2Controller,消息发送子日志V2,/v2/atomic/bbpmMessageSendSubLogV2Entity,selectByPageRecord,GET,/selectByPage,/v2/atomic/bbpmMessageSendSubLogV2Entity/selectByPage,分页查询,分页查询,binghong.tang
adjust,BbpmReceivableAdjustController,应收调整,/v2/business/bbpmReceivableAdjustEntity,selectByPageRecord,GET,/selectByPage,/v2/business/bbpmReceivableAdjustEntity/selectByPage,分页查询,分页查询,yuanxuesong
adjust,BbpmReceivableAdjustBillController,应收调整账单表,/bbpmReceivableAdjustBillEntity,selectByPageRecord,GET,/selectByPage,/bbpmReceivableAdjustBillEntity/selectByPage,分页查询,分页查询,yuanxuesong
adjust,BbpmReceivableAdjustExamineController,应收调整审核表,/bbpmReceivableAdjustExamineEntity,selectByPageRecord,GET,/selectByPage,/bbpmReceivableAdjustExamineEntity/selectByPage,分页查询,分页查询,yuanxuesong
